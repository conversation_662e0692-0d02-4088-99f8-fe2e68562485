import { Head<PERSON> } from "@/layouts";
import React from "react";
import KY<PERSON>ontent from "./parts/KycContent";
import KYCErrorFallback from "./parts/KycErrorFallback";
import { cookies } from "next/headers";
import { COOKIES_ACCESS_TOKEN_KEY } from "@/constants/common";
import config from "@/config/index";

// Error types for KYC access token failures
const ACCESS_TOKEN_ERROR_TYPES = {
  UNAUTHORIZED: "unauthorized",
  NETWORK: "network",
  SERVER: "server",
  UNKNOWN: "unknown",
} as const;

type AccessTokenError = {
  type: keyof typeof ACCESS_TOKEN_ERROR_TYPES;
  message: string;
  statusCode?: number;
};

async function createAccessToken(): Promise<{
  sumsubAccessToken?: string;
  error?: AccessTokenError;
}> {
  const accessToken =
    (await cookies()).get(COOKIES_ACCESS_TOKEN_KEY)?.value || "";

  // Check if user access token exists
  if (!accessToken) {
    console.error("No access token found in cookies");
    return {
      error: {
        type: "UNAUTHORIZED",
        message: "Authentication required. Please log in to continue.",
      },
    };
  }

  try {
    const url = `${config.apiUrl}/v1/kyc/verification`;
    const res = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
      cache: "no-store",
    });

    if (!res.ok) {
      const errorText = await res.text();
      console.error("KYC access token API error:", {
        status: res.status,
        statusText: res.statusText,
        error: errorText,
      });

      // Determine error type based on status code
      let errorType: keyof typeof ACCESS_TOKEN_ERROR_TYPES = "UNKNOWN";
      let userMessage =
        "Failed to initialize KYC verification. Please try again.";

      if (res.status === 401 || res.status === 403) {
        errorType = "UNAUTHORIZED";
        userMessage =
          "Authentication failed. Please log in again and try again.";
      } else if (res.status >= 500) {
        errorType = "SERVER";
        userMessage =
          "Server error occurred. Please try again later or contact support if the issue persists.";
      } else if (res.status >= 400) {
        errorType = "SERVER";
        userMessage =
          "Unable to initialize KYC verification. Please try again or contact support.";
      }

      return {
        error: {
          type: errorType,
          message: userMessage,
          statusCode: res.status,
        },
      };
    }

    const data = await res.json();
    return { sumsubAccessToken: data?.sumsubAccessToken };
  } catch (e: any) {
    console.error("Error creating access token:", e?.message);

    // Determine if it's a network error or other error
    const isNetworkError =
      e?.code === "ENOTFOUND" ||
      e?.code === "ECONNREFUSED" ||
      e?.name === "TypeError";

    return {
      error: {
        type: isNetworkError ? "NETWORK" : "UNKNOWN",
        message: isNetworkError
          ? "Network connection error. Please check your internet connection and try again."
          : "An unexpected error occurred. Please try again or contact support if the issue persists.",
      },
    };
  }
}

interface KYCPageProps {
  searchParams: Promise<{
    level?: string;
  }>;
}

export default async function KYCPage({ searchParams }: KYCPageProps) {
  const params = await searchParams;
  const kycLevel = params.level || "basic";
  const result = await createAccessToken();

  return (
    <div>
      <Header />
      {result.error ? (
        <KYCErrorFallback error={result.error} />
      ) : (
        <KYCContent
          accessToken={result.sumsubAccessToken || ""}
          kycLevel={kycLevel}
        />
      )}
    </div>
  );
}
