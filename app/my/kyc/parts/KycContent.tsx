"use client";

import SumsubWebSdk from "@sumsub/websdk-react";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
import { useRouter } from "next/navigation";
import { successMsg, errorMsg, MsgWithTitle } from "@/libs/toast";
import LoadingSpinner from "@/components/LoadingSpinner";

// Constants for KYC status
const KYC_STATUS = {
  GREEN: "GREEN",
  RED: "RED",
  GRAY: "GRAY",
} as const;

// Constants for message types
const MESSAGE_TYPES = {
  APPLICANT_REVIEWED: "idCheck.onApplicantReviewed",
  APPLICANT_SUBMITTED: "idCheck.onApplicantSubmitted",
  APPLICANT_PENDING: "idCheck.onApplicantPending",
  APPLICANT_REJECTED: "idCheck.onApplicantRejected",
  APPLICANT_APPROVED: "idCheck.onApplicantApproved",
  APPLICANT_RESET: "idCheck.onApplicantReset",
  APPLICANT_EXPIRED: "idCheck.onApplicantExpired",
  APPLICANT_DELETED: "idCheck.onApplicantDeleted",
  APPLICANT_ACTION_REQUIRED: "idCheck.onApplicantActionRequired",
  APPLICANT_PRECHECKED: "idCheck.onApplicantPrechecked",
} as const;

// Error types
const ERROR_TYPES = {
  NETWORK: "network",
  AUTH: "auth",
  VALIDATION: "validation",
  SERVER: "server",
} as const;

interface KYCContentProps {
  accessToken: string;
  kycLevel?: string;
}

const KYCContent = ({ accessToken, kycLevel = "basic" }: KYCContentProps) => {
  const userInfo = useSelector((state: RootState) => state.user.userInfo);
  const router = useRouter();
  const [isLoadingSumsubSDK, setIsLoadingSumsupSDK] = useState(true);

  useEffect(() => {
    setTimeout(() => {
      setIsLoadingSumsupSDK(false);
    }, 2000);
  }, []);

  // Helper function to navigate to identification page
  const navigateToIdentification = () => {
    router.push("/my/identification");
  };

  // Helper function to handle KYC result based on review answer
  const handleKYCReviewResult = (payload: any) => {
    const reviewAnswer = payload?.reviewResult?.reviewAnswer;
    const reviewComment = payload?.reviewResult?.reviewComment;

    console.log("🔍 KYC Review Result:", {
      answer: reviewAnswer,
      comment: reviewComment,
    });

    switch (reviewAnswer) {
      case KYC_STATUS.GREEN:
        console.log("✅ KYC Verification successful!");
        successMsg("KYC verification successful!");
        navigateToIdentification();
        break;

      case KYC_STATUS.RED:
        console.log("❌ KYC Verification rejected!");
        const errorMessage =
          reviewComment || "KYC verification rejected. Please try again.";
        errorMsg(errorMessage);
        navigateToIdentification();
        break;

      case KYC_STATUS.GRAY:
        console.log("⚠️ KYC Verification pending review!");
        MsgWithTitle(
          "Notification",
          "KYC verification is pending review. We will notify you when the result is ready."
        );
        navigateToIdentification();
        break;

      default:
        console.log("❓ Unknown KYC status:", reviewAnswer);
        MsgWithTitle(
          "Notification",
          "Unknown verification status. Please contact support."
        );
        navigateToIdentification();
        break;
    }
  };

  // Helper function to handle errors
  const handleError = (error: any) => {
    console.error("❌ Sumsub error:", error);

    let errorMessage =
      "An error occurred during verification. Please try again.";

    if (error?.type === ERROR_TYPES.NETWORK) {
      errorMessage =
        "Network connection error. Please check your connection and try again.";
    } else if (error?.type === ERROR_TYPES.AUTH) {
      errorMessage = "Authentication error. Please login again.";
      navigateToIdentification();
    } else if (error?.type === ERROR_TYPES.VALIDATION) {
      errorMessage = "Invalid data. Please check your information.";
    } else if (error?.type === ERROR_TYPES.SERVER) {
      errorMessage = "Server error. Please try again later.";
    }

    errorMsg(errorMessage);

    // Navigate to identification page after error (with delay for auth errors)
    setTimeout(
      () => {
        navigateToIdentification();
      },
      error?.type === ERROR_TYPES.AUTH ? 0 : 2000
    );
  };

  // Helper function to handle session expiration
  const handleSessionExpired = () => {
    console.log("KYC session expired");
    MsgWithTitle("Notification", "KYC session has expired. Please try again.");
    navigateToIdentification();
  };

  return (
    <div className="fixed inset-0 top-[50px] overflow-auto">
      <div
        className={`flex h-screen items-center justify-center ${
          !isLoadingSumsubSDK ? "hidden" : ""
        }`}
      >
        <LoadingSpinner message="Loading KYC verification..." />
      </div>
      <SumsubWebSdk
        key={`sumsub-${kycLevel}`}
        accessToken={accessToken}
        expirationHandler={handleSessionExpired}
        style={{
          width: "100%",
          height: "100%",
          border: "none",
          display: isLoadingSumsubSDK ? "hidden" : "block",
        }}
        config={{
          lang: "en",
          email: userInfo?.email || "",
          customizationName: "customized_vdax",
        }}
        onMessage={(type: any, payload: any) => {
          console.log("[SumsubSDK]🔥 onMessage:", type, payload);

          switch (type) {
            case MESSAGE_TYPES.APPLICANT_REVIEWED:
              console.log("✅ Applicant was reviewed");
              handleKYCReviewResult(payload);
              break;

            case MESSAGE_TYPES.APPLICANT_SUBMITTED:
              console.log("📝 Applicant submitted documents");
              successMsg("Documents submitted successfully!");
              navigateToIdentification();
              break;

            case MESSAGE_TYPES.APPLICANT_PENDING:
              console.log("⏳ Applicant is pending");
              MsgWithTitle(
                "Notification",
                "Verification is pending. Please wait."
              );
              navigateToIdentification();
              break;

            case MESSAGE_TYPES.APPLICANT_REJECTED:
              console.log("❌ Applicant was rejected");
              const rejectReason =
                payload?.reviewResult?.reviewComment || "Verification rejected";
              errorMsg(rejectReason);
              navigateToIdentification();
              break;

            case MESSAGE_TYPES.APPLICANT_APPROVED:
              console.log("✅ Applicant was approved");
              successMsg("Verification approved!");
              navigateToIdentification();
              break;

            case MESSAGE_TYPES.APPLICANT_RESET:
              console.log("🔄 Applicant was reset");
              MsgWithTitle(
                "Notification",
                "Verification process has been reset."
              );
              break;

            case MESSAGE_TYPES.APPLICANT_EXPIRED:
              console.log("⏰ Applicant session expired");
              MsgWithTitle(
                "Notification",
                "Verification session has expired. Please try again."
              );
              navigateToIdentification();
              break;

            case MESSAGE_TYPES.APPLICANT_DELETED:
              console.log("🗑️ Applicant was deleted");
              errorMsg("Verification account has been deleted.");
              navigateToIdentification();
              break;

            case MESSAGE_TYPES.APPLICANT_ACTION_REQUIRED:
              console.log("⚠️ Action required from applicant");
              MsgWithTitle(
                "Action Required",
                "Action required from you. Please check and complete."
              );
              navigateToIdentification();
              break;

            case MESSAGE_TYPES.APPLICANT_PRECHECKED:
              console.log("🔍 Applicant pre-check completed");
              successMsg("Pre-check completed!");
              navigateToIdentification();
              break;

            default:
              console.log("📨 Unknown message type:", type, payload);
              break;
          }
        }}
        onError={handleError}
      />
    </div>
  );
};

export default KYCContent;
