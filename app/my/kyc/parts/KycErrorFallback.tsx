"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { AppButton } from "@/components";
import { MessageCriticalIcon, RefreshIcon } from "@/assets/icons";

// Error types for KYC access token failures
const ACCESS_TOKEN_ERROR_TYPES = {
  UNAUTHORIZED: "unauthorized",
  NETWORK: "network",
  SERVER: "server", 
  UNKNOWN: "unknown",
} as const;

type AccessTokenError = {
  type: keyof typeof ACCESS_TOKEN_ERROR_TYPES;
  message: string;
  statusCode?: number;
};

interface KYCErrorFallbackProps {
  error: AccessTokenError;
}

const KYCErrorFallback = ({ error }: KYCErrorFallbackProps) => {
  const router = useRouter();

  // Helper function to get error-specific actions
  const getErrorActions = () => {
    switch (error.type) {
      case "UNAUTHORIZED":
        return {
          primaryAction: {
            label: "Go to Login",
            onClick: () => router.push("/login"),
            variant: "buy" as const,
          },
          secondaryAction: {
            label: "Back to Dashboard",
            onClick: () => router.push("/my/dashboard"),
            variant: "outline" as const,
          },
        };

      case "NETWORK":
        return {
          primaryAction: {
            label: "Try Again",
            onClick: () => window.location.reload(),
            variant: "buy" as const,
          },
          secondaryAction: {
            label: "Back to Dashboard", 
            onClick: () => router.push("/my/dashboard"),
            variant: "outline" as const,
          },
        };

      case "SERVER":
        return {
          primaryAction: {
            label: "Try Again",
            onClick: () => window.location.reload(),
            variant: "buy" as const,
          },
          secondaryAction: {
            label: "Contact Support",
            onClick: () => window.open("mailto:<EMAIL>", "_blank"),
            variant: "outline" as const,
          },
        };

      default:
        return {
          primaryAction: {
            label: "Try Again",
            onClick: () => window.location.reload(),
            variant: "buy" as const,
          },
          secondaryAction: {
            label: "Contact Support",
            onClick: () => window.open("mailto:<EMAIL>", "_blank"),
            variant: "outline" as const,
          },
        };
    }
  };

  const { primaryAction, secondaryAction } = getErrorActions();

  // Helper function to get error title
  const getErrorTitle = () => {
    switch (error.type) {
      case "UNAUTHORIZED":
        return "Authentication Required";
      case "NETWORK":
        return "Connection Error";
      case "SERVER":
        return "Service Unavailable";
      default:
        return "KYC Verification Error";
    }
  };

  return (
    <div className="flex min-h-[calc(100vh-50px)] items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="bg-white-50 border-white-100 rounded-2xl border p-8 text-center">
          {/* Error Icon */}
          <div className="mb-6 flex justify-center">
            <div className="bg-red-900 flex h-16 w-16 items-center justify-center rounded-full">
              <MessageCriticalIcon className="h-8 w-8 text-red-400" />
            </div>
          </div>

          {/* Error Title */}
          <h1 className="heading-lg-semibold-24 mb-4 text-white-1000">
            {getErrorTitle()}
          </h1>

          {/* Error Message */}
          <p className="body-md-regular-14 text-white-700 mb-8">
            {error.message}
          </p>

          {/* Error Details (for debugging, only show in development) */}
          {process.env.NODE_ENV === "development" && error.statusCode && (
            <div className="bg-white-100 mb-6 rounded-lg p-3">
              <p className="body-sm-regular-12 text-white-500">
                Error Code: {error.statusCode} | Type: {error.type}
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-3">
            <AppButton
              variant={primaryAction.variant}
              onClick={primaryAction.onClick}
              className="w-full"
            >
              <RefreshIcon className="mr-2 h-4 w-4" />
              {primaryAction.label}
            </AppButton>

            <AppButton
              variant={secondaryAction.variant}
              onClick={secondaryAction.onClick}
              className="w-full"
            >
              {secondaryAction.label}
            </AppButton>
          </div>

          {/* Additional Help Text */}
          <div className="mt-6 pt-6 border-t border-white-100">
            <p className="body-sm-regular-12 text-white-500">
              If this problem persists, please{" "}
              <button
                onClick={() => window.open("mailto:<EMAIL>", "_blank")}
                className="text-green-500 hover:text-green-400 underline"
              >
                contact our support team
              </button>{" "}
              for assistance.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default KYCErrorFallback;
